import { useState } from "react";
import { profileService } from "../services/profileService";

interface PasswordModalProps {
  show: boolean;
  onClose: () => void;
}

const PasswordModal = ({ show, onClose }: PasswordModalProps) => {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Password Validation Rules
  const hasMinLength = newPassword.length >= 8;
  const hasUpperCase = /[A-Z]/.test(newPassword);
  const hasNumber = /\d/.test(newPassword);
  const hasSpecialChar = /[^A-Za-z0-9]/.test(newPassword);

  const confirmPasswordMatches = newPassword === confirmPassword;

  const isFormValid =
    currentPassword.trim() !== "" &&
    hasMinLength &&
    hasUpperCase &&
    hasNumber &&
    hasSpecialChar &&
    confirmPasswordMatches;

  const handleReset = async () => {
    if (!isFormValid) return;

    try {
      setLoading(true);
      setError(null);
      
      await profileService.changePassword({
        currentPassword,
        newPassword,
      });

      alert("Password changed successfully!");
      handleClose();
    } catch (err) {
      console.error("Error changing password:", err);
      setError(err instanceof Error ? err.message : "Failed to change password");
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
    setError(null);
    onClose();
  };

  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 animate-fade-in">
      <div className="bg-white py-6 px-7 rounded-xl shadow-xl w-full max-w-md relative">
        <h2 className="text-xl font-semibold mb-6 text-red-700 text-center">
          Change Password
        </h2>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <form className="space-y-4 pb-16">
          {/* Current Password */}
          <div className="flex flex-col">
            <label htmlFor="currentPassword" className="text-sm font-medium mb-1">
              Current Password
            </label>
            <input
              id="currentPassword"
              type="password"
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* New Password */}
          <div className="flex flex-col">
            <label htmlFor="newPassword" className="text-sm font-medium mb-1">
              New Password
            </label>
            <input
              id="newPassword"
              type="password"
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* Confirm Password */}
          <div className="flex flex-col">
            <label htmlFor="confirmPassword" className="text-sm font-medium mb-1">
              Confirm New Password
            </label>
            <input
              id="confirmPassword"
              type="password"
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* Password Requirements */}
          <div className="text-xs text-gray-600 space-y-1">
            <p className="font-medium">Password must contain:</p>
            <ul className="space-y-1">
              <li className={hasMinLength ? "text-green-600" : "text-red-600"}>
                ✓ At least 8 characters
              </li>
              <li className={hasUpperCase ? "text-green-600" : "text-red-600"}>
                ✓ One uppercase letter
              </li>
              <li className={hasNumber ? "text-green-600" : "text-red-600"}>
                ✓ One number
              </li>
              <li className={hasSpecialChar ? "text-green-600" : "text-red-600"}>
                ✓ One special character
              </li>
              <li className={confirmPasswordMatches ? "text-green-600" : "text-red-600"}>
                ✓ Passwords match
              </li>
            </ul>
          </div>
        </form>

        {/* Footer Buttons */}
        <div className="absolute bottom-6 right-6 flex gap-2">
          <button
            className="bg-gray-300 px-4 py-2 rounded-md hover:bg-gray-400 disabled:opacity-50"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            className={`px-4 py-2 rounded-md text-white transition ${
              isFormValid && !loading
                ? "bg-red-700 hover:bg-red-800"
                : "bg-red-300 cursor-not-allowed"
            }`}
            onClick={handleReset}
            disabled={!isFormValid || loading}
          >
            {loading ? "Changing..." : "Change Password"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PasswordModal;
