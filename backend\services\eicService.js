import User from "../models/User.js";
import bcrypt from "bcryptjs";
import { generateEICFacultyId } from "../utils/facultyIdGenerator.js";

// Generate secure password
const generateSecurePassword = (length = 8) => {
  const minLength = Math.max(length, 8);
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const digits = "0123456789";
  const special = "!@#$%^&*()_+-=[]{}|;:,.<>?";
  const all = uppercase + lowercase + digits + special;

  const getRandom = (chars) => chars[Math.floor(Math.random() * chars.length)];

  let password = [
    getRandom(uppercase),
    getRandom(digits),
    getRandom(special),
    getRandom(lowercase),
  ];

  for (let i = password.length; i < minLength; i++) {
    password.push(getRandom(all));
  }

  return password.sort(() => Math.random() - 0.5).join("");
};

export const eicService = {
  // Get EIC user
  async getEIC() {
    try {
      const eic = await User.findOne({
        role: "Exam Cell"
      }).select("name email role facultyID passwordHash");

      if (!eic) {
        // Return default structure if no EIC exists
        return {
          email: "<EMAIL>",
          username: "examcelleic",
          hasPassword: false,
          exists: false
        };
      }

      return {
        id: eic._id,
        name: eic.name,
        email: eic.email,
        username: eic.email.split('@')[0],
        role: eic.role,
        hasPassword: !!eic.passwordHash,
        exists: true
      };
    } catch (error) {
      console.error("Error fetching EIC:", error);
      throw error;
    }
  },

  // Create or update EIC credentials
  async createOrUpdateEIC({ email, username, password, createdBy }) {
    try {
      // Check if EIC already exists
      let eic = await User.findOne({ role: "Exam Cell" });

      const userData = {
        email: email,
        username: username || email.split('@')[0], // Set username properly
        role: "Exam Cell",
        name: "Exam Cell (EIC)",
        facultyID: await generateEICFacultyId(), // Clean format like EIC0001
        permissions: ["manage_exams", "view_all_reports", "manage_gradesheets"]
      };

      let generatedPassword = null;

      // If password is provided, hash it
      if (password) {
        userData.passwordHash = await bcrypt.hash(password, 10);
      } else if (!eic) {
        // Generate password for new EIC
        generatedPassword = generateSecurePassword();
        userData.passwordHash = await bcrypt.hash(generatedPassword, 10);
      }

      if (eic) {
        // Update existing EIC
        Object.assign(eic, userData);
        await eic.save();
      } else {
        // Create new EIC
        eic = new User(userData);
        await eic.save();
      }

      return {
        eic: {
          id: eic._id,
          name: eic.name,
          email: eic.email,
          username: eic.username,
          role: eic.role,
          hasPassword: !!eic.passwordHash
        },
        generatedPassword: generatedPassword
      };
    } catch (error) {
      console.error("Error creating/updating EIC:", error);
      throw error;
    }
  },

  // Reset EIC password
  async resetEICPassword() {
    try {
      const newPassword = generateSecurePassword();
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      const eic = await User.findOneAndUpdate(
        { role: "Exam Cell" },
        { passwordHash: hashedPassword },
        { new: true }
      );

      if (!eic) {
        throw new Error("EIC not found");
      }

      return {
        eic,
        newPassword
      };
    } catch (error) {
      console.error("Error resetting EIC password:", error);
      throw error;
    }
  },

  // Update EIC password
  async updateEICPassword(newPassword) {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      const eic = await User.findOneAndUpdate(
        { role: "Exam Cell" },
        { passwordHash: hashedPassword },
        { new: true }
      );

      if (!eic) {
        throw new Error("EIC not found");
      }

      return { eic };
    } catch (error) {
      console.error("Error updating EIC password:", error);
      throw error;
    }
  }
};

export default eicService;
