import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import connectDB from "./config/db.js";
import { runInitialSeeds, needsSeeding } from "./utils/seedDatabase.js";
import courseOfferingRoutes from "./routes/courseOffering.routes.js";
import authRoutes from "./routes/auth.routes.js";
import userRoutes from "./routes/user.routes.js";
import courseRoutes from "./routes/courseDetail.routes.js";
import gradesheetRoutes from "./routes/gradeSheet.routes.js";
import ipRoutes from "./routes/ip.routes.js";
import emailRoutes from "./routes/email.routes.js";
import studentRoutes from "./routes/student.routes.js";
import departmentRoutes from "./routes/department.routes.js";
import healthRoutes from "./routes/health.routes.js";
import cisCredentialRoutes from "./routes/cisCredential.routes.js";
import hodRoutes from "./routes/hod.routes.js";
import eicRoutes from "./routes/eic.routes.js";
import principalRoutes from "./routes/principal.routes.js";
import courseCoordinatorRoutes from "./routes/courseCoordinator.routes.js";
import profileRoutes from "./routes/profile.routes.js";

dotenv.config();

// Initialize database and seeding
const initializeApp = async () => {
  try {
    // Connect to database
    await connectDB();

    // Check if initial seeding is needed and run it
    if (await needsSeeding()) {
      console.log("🌱 New database detected. Running initial seeding...");
      await runInitialSeeds();
    }

    console.log("✅ Application initialization completed");
  } catch (error) {
    console.error("❌ Application initialization failed:", error);
    process.exit(1);
  }
};

initializeApp();

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const corsOptions = {
  origin:
    process.env.NODE_ENV === "production"
      ? process.env.FRONTEND_URL || "http://localhost:5173"
      : ["http://localhost:5173", "http://127.0.0.1:5173"],
  methods: ["GET", "POST", "PUT", "DELETE"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true,
};
app.use(cors(corsOptions));

// Routes
app.use("/api/course-offering", courseOfferingRoutes);
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/courses", courseRoutes);
app.use("/api/gradesheets", gradesheetRoutes);
app.use("/api/ip", ipRoutes);
app.use("/api/email", emailRoutes);
app.use("/api/students", studentRoutes);
app.use("/api/departments", departmentRoutes);
app.use("/api/health", healthRoutes);
app.use("/api/cis-credentials", cisCredentialRoutes);
app.use("/api/hod-credentials", hodRoutes);
app.use("/api/eic", eicRoutes);
app.use("/api/principal", principalRoutes);
app.use("/api/course-coordinators", courseCoordinatorRoutes);
app.use("/api/profile", profileRoutes);
const PORT = process.env.PORT || 7000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
