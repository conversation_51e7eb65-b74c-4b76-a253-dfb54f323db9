import express from "express";
import {
  getMyProfile,
  updateMyProfile,
  changePassword,
} from "../controllers/profileController.js";
import { protect } from "../middleware/authMiddleware.js";

const router = express.Router();

// All routes require authentication
router.use(protect);

// Get current user profile
router.get("/me", getMyProfile);

// Update current user profile
router.put("/update", updateMyProfile);

// Change password
router.put("/change-password", changePassword);

export default router;
