import User from "../models/User.js";
import bcrypt from "bcryptjs";
import { generatePrincipalFacultyId } from "../utils/facultyIdGenerator.js";

// Generate secure password
const generateSecurePassword = (length = 8) => {
  const minLength = Math.max(length, 8);
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const digits = "0123456789";
  const special = "!@#$%^&*()_+-=[]{}|;:,.<>?";
  const all = uppercase + lowercase + digits + special;

  const getRandom = (chars) => chars[Math.floor(Math.random() * chars.length)];

  let password = [
    getRandom(uppercase),
    getRandom(digits),
    getRandom(special),
    getRandom(lowercase),
  ];

  for (let i = password.length; i < minLength; i++) {
    password.push(getRandom(all));
  }

  return password.sort(() => Math.random() - 0.5).join("");
};

export const principalService = {
  // Get Principal user
  async getPrincipal() {
    try {
      const principal = await User.findOne({
        role: "Principal"
      }).select("name email role facultyID passwordHash");

      if (!principal) {
        // Return default structure if no Principal exists
        return {
          email: "<EMAIL>",
          username: "principalkjsse",
          hasPassword: false,
          exists: false
        };
      }

      return {
        id: principal._id,
        name: principal.name,
        email: principal.email,
        username: principal.email.split('@')[0],
        role: principal.role,
        hasPassword: !!principal.passwordHash,
        exists: true
      };
    } catch (error) {
      console.error("Error fetching Principal:", error);
      throw error;
    }
  },

  // Create or update Principal credentials
  async createOrUpdatePrincipal({ email, username, password, createdBy }) {
    try {
      // Check if Principal already exists
      let principal = await User.findOne({ role: "Principal" });

      const userData = {
        email: email,
        username: username || email.split('@')[0], // Set username properly
        role: "Principal",
        name: "Principal",
        facultyID: await generatePrincipalFacultyId(), // Clean format like PRIN0001
        permissions: ["manage_all", "view_all_reports", "manage_departments", "manage_users"]
      };

      let generatedPassword = null;

      // If password is provided, hash it
      if (password) {
        userData.passwordHash = await bcrypt.hash(password, 10);
      } else if (!principal) {
        // Generate password for new Principal
        generatedPassword = generateSecurePassword();
        userData.passwordHash = await bcrypt.hash(generatedPassword, 10);
      }

      if (principal) {
        // Update existing Principal
        Object.assign(principal, userData);
        await principal.save();
      } else {
        // Create new Principal
        principal = new User(userData);
        await principal.save();
      }

      return {
        principal: {
          id: principal._id,
          name: principal.name,
          email: principal.email,
          username: principal.username,
          role: principal.role,
          hasPassword: !!principal.passwordHash
        },
        generatedPassword: generatedPassword
      };
    } catch (error) {
      console.error("Error creating/updating Principal:", error);
      throw error;
    }
  },

  // Reset Principal password
  async resetPrincipalPassword() {
    try {
      const newPassword = generateSecurePassword();
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      const principal = await User.findOneAndUpdate(
        { role: "Principal" },
        { passwordHash: hashedPassword },
        { new: true }
      );

      if (!principal) {
        throw new Error("Principal not found");
      }

      return {
        principal,
        newPassword
      };
    } catch (error) {
      console.error("Error resetting Principal password:", error);
      throw error;
    }
  },

  // Update Principal password
  async updatePrincipalPassword(newPassword) {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      const principal = await User.findOneAndUpdate(
        { role: "Principal" },
        { passwordHash: hashedPassword },
        { new: true }
      );

      if (!principal) {
        throw new Error("Principal not found");
      }

      return { principal };
    } catch (error) {
      console.error("Error updating Principal password:", error);
      throw error;
    }
  }
};

export default principalService;
