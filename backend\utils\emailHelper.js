import nodemailer from "nodemailer";

// Create transporter with better error handling
const createTransporter = () => {
  return nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD,
    },
    timeout: 15000, // 15 second timeout
    connectionTimeout: 15000, // 15 second connection timeout
    pool: true, // Use connection pooling
    maxConnections: 5,
    maxMessages: 100,
    rateDelta: 20000, // Wait 20 seconds between emails
    rateLimit: 5 // Max 5 emails per rateDelta period
  });
};

// Send credentials email with retry logic
export const sendCredentialsEmail = async (emailData) => {
  const { email, name, username, password, role, department } = emailData;
  
  console.log(`📧 Attempting to send credentials email to ${email}...`);
  
  // Validate input
  if (!email || !name || !username || !password || !role) {
    const error = new Error("Missing required email data");
    console.error("❌ Email validation failed:", {
      email: !!email,
      name: !!name,
      username: !!username,
      password: !!password,
      role: !!role
    });
    throw error;
  }

  // Check email configuration
  if (!process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
    const error = new Error("Email configuration not found");
    console.error("❌ Email config missing:", {
      EMAIL_USER: !!process.env.EMAIL_USER,
      EMAIL_PASSWORD: !!process.env.EMAIL_PASSWORD
    });
    throw error;
  }

  const transporter = createTransporter();
  
  // Verify connection first
  try {
    console.log("🔍 Verifying email connection...");
    await transporter.verify();
    console.log("✅ Email connection verified");
  } catch (verifyError) {
    console.error("❌ Email verification failed:", verifyError.message);
    
    // If Gmail fails, try to provide helpful debugging info
    if (verifyError.message.includes('Invalid login')) {
      console.error("💡 Hint: Check if Gmail App Password is correct and 2FA is enabled");
    } else if (verifyError.message.includes('timeout')) {
      console.error("💡 Hint: Network timeout - check internet connection");
    }
    
    throw new Error(`Email service verification failed: ${verifyError.message}`);
  }

  const roleDisplayNames = {
    cis: "CIS Coordinator",
    cc: "Course Coordinator", 
    hod: "Head of Department",
    faculty: "Faculty",
    eic: "Exam Cell (EIC)",
    principal: "Principal"
  };

  const roleDisplay = roleDisplayNames[role] || role;
  const departmentText = department ? ` for ${department} department` : '';

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: `Your ${roleDisplay} Account Credentials - CIS System`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h2 style="color: #ED1C24; margin: 0;">K.J. Somaiya College of Engineering</h2>
          <p style="color: #666; margin: 5px 0 0 0;">Continuous Internal Systems</p>
        </div>
        
        <div style="background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="color: #333; margin: 0 0 15px 0;">Account Assignment Notification</h3>
          <p style="margin: 0 0 10px 0;">Dear <strong>${name}</strong>,</p>
          <p style="margin: 0 0 15px 0;">You have been assigned as <strong>${roleDisplay}</strong>${departmentText}. Below are your login credentials:</p>
        </div>
        
        <div style="background-color: #fff; border: 2px solid #ED1C24; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <h4 style="color: #ED1C24; margin: 0 0 15px 0;">Login Credentials</h4>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #333;">Username:</td>
              <td style="padding: 8px 0; color: #666;">${username}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #333;">Password:</td>
              <td style="padding: 8px 0; color: #666; font-family: monospace; background-color: #f5f5f5; padding: 4px 8px; border-radius: 3px;">${password}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; font-weight: bold; color: #333;">Role:</td>
              <td style="padding: 8px 0; color: #666;">${roleDisplay}</td>
            </tr>
            ${department ? `<tr><td style="padding: 8px 0; font-weight: bold; color: #333;">Department:</td><td style="padding: 8px 0; color: #666;">${department}</td></tr>` : ''}
          </table>
        </div>
        
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <p style="margin: 0; color: #856404;"><strong>Important Security Notice:</strong></p>
          <ul style="margin: 10px 0 0 0; color: #856404;">
            <li>Please change your password after first login</li>
            <li>Do not share your credentials with anyone</li>
            <li>Keep your login information secure</li>
          </ul>
        </div>
        
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px; margin: 0;">
            If you have any questions, please contact the system administrator.
          </p>
          <p style="color: #999; font-size: 12px; margin: 10px 0 0 0;">
            This is an automated message. Please do not reply to this email.
          </p>
        </div>
      </div>
    `,
  };

  // Send email with retry logic
  const maxRetries = 3;
  let lastError = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`📤 Sending email attempt ${attempt}/${maxRetries} to ${email}...`);
      
      const info = await transporter.sendMail(mailOptions);
      
      console.log(`✅ Email sent successfully to ${email}!`);
      console.log(`📧 Message ID: ${info.messageId}`);
      
      return {
        success: true,
        messageId: info.messageId,
        email: email
      };
      
    } catch (sendError) {
      lastError = sendError;
      console.error(`❌ Email send attempt ${attempt} failed:`, sendError.message);
      
      if (attempt < maxRetries) {
        const waitTime = attempt * 2000; // Wait 2s, 4s, 6s between retries
        console.log(`⏳ Waiting ${waitTime}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
  
  // All retries failed
  console.error(`💥 All ${maxRetries} email send attempts failed for ${email}`);
  throw new Error(`Failed to send email after ${maxRetries} attempts: ${lastError.message}`);
};

// Test email configuration
export const testEmailConfig = async () => {
  console.log("🧪 Testing email configuration...");

  if (!process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
    throw new Error("Email configuration missing");
  }
  
  const transporter = createTransporter();
  
  try {
    await transporter.verify();
    console.log("✅ Email configuration test passed");
    return { success: true, message: "Email configuration is valid" };
  } catch (error) {
    console.error("❌ Email configuration test failed:", error.message);
    throw new Error(`Email configuration test failed: ${error.message}`);
  }
};

export default { sendCredentialsEmail, testEmailConfig };
