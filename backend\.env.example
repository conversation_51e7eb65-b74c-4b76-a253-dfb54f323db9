# Database Configuration
MONGO_URI=mongodb://127.0.0.1:27017/CIS

# Server Configuration
PORT=7000

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here

# Email Configuration (Gmail)
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password_here

# Frontend URL
FRONTEND_URL=http://localhost:5173

# Email Setup Instructions:
# 1. Enable 2-Factor Authentication on your Gmail account
# 2. Generate an App Password:
#    - Go to Google Account settings
#    - Security > 2-Step Verification > App passwords
#    - Generate a new app password for "Mail"
#    - Use this app password as EMAIL_PASSWORD (not your regular Gmail password)
# 3. Replace EMAIL_USER with your Gmail address
# 4. Replace EMAIL_PASSWORD with the generated app password
