import User from "../models/User.js";
import CourseOffering from "../models/courseOffering.js";
import bcrypt from "bcryptjs";
import { generateCCFacultyId } from "../utils/facultyIdGenerator.js";

// Generate secure password
const generateSecurePassword = (length = 8) => {
  const minLength = Math.max(length, 8);
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const digits = "0123456789";
  const special = "!@#$%^&*()_+-=[]{}|;:,.<>?";
  const all = uppercase + lowercase + digits + special;

  const getRandom = (chars) => chars[Math.floor(Math.random() * chars.length)];

  let password = [
    getRandom(uppercase),
    getRandom(digits),
    getRandom(special),
    getRandom(lowercase),
  ];

  for (let i = password.length; i < minLength; i++) {
    password.push(getRandom(all));
  }

  return password.sort(() => Math.random() - 0.5).join("");
};

// Generate username for Course Coordinator using course code
const generateCourseCoordinatorUsername = async (courseCode, existingUsernames = []) => {
  try {
    // Clean the course code for username generation
    let cleanCourseCode = courseCode.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
    
    let base = `CC${cleanCourseCode}`;
    let final = base;
    let i = 1;
    
    while (existingUsernames && existingUsernames.includes(final.toLowerCase())) {
      final = `${base}${i++}`;
    }
    
    return final.toLowerCase(); // Store as lowercase but generate with uppercase
  } catch (error) {
    console.error("Error generating Course Coordinator username:", error);
    // Fallback to basic generation
    let cleanCourseCode = courseCode.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
    let base = `CC${cleanCourseCode}`;
    let final = base;
    let i = 1;
    
    while (existingUsernames && existingUsernames.includes(final.toLowerCase())) {
      final = `${base}${i++}`;
    }
    
    return final.toLowerCase();
  }
};

export const courseCoordinatorService = {
  // Get all Course Coordinators for a specific academic year and optionally department
  async getCourseCoordinators(academicYear, department = null) {
    try {
      console.log("🔧 [Service] getCourseCoordinators called with:", { academicYear, department });
      
      const filter = {
        role: "Course Coordinator",
        academicYear: academicYear
      };
      
      if (department) {
        filter.department = department;
      }

      console.log("🔍 [Service] Searching with filter:", filter);
      const courseCoordinators = await User.find(filter)
        .select("name email department progDept academicYear facultyID courseCode courseName yearSem");

      console.log("✅ [Service] Found course coordinators:", {
        count: courseCoordinators.length,
        coordinators: courseCoordinators
      });

      return courseCoordinators;
    } catch (error) {
      console.error("❌ [Service] Error fetching Course Coordinators:", error);
      throw error;
    }
  },

  // Create Course Coordinator credentials for all courses in a department (similar to HoD)
  async createCourseCoordinatorCredentials(departments, academicYear, createdBy) {
    try {
      console.log("🔧 [Service] createCourseCoordinatorCredentials called with:", { departments, academicYear, createdBy });
      const createdCredentials = [];

      for (const department of departments) {
        console.log(`🏢 [Service] Processing department: ${department}`);
        
        // First, let's check what academic years and departments exist in CourseOffering
        console.log("🔍 [Service] Checking all CourseOffering data...");
        const allCourses = await CourseOffering.find({}).select('courseCode courseName yearSem type department academicYear status');
        console.log(`📊 [Service] Total courses in database: ${allCourses.length}`);
        
        // Group by academic year and department to see what's available
        const academicYearGroups = {};
        const departmentGroups = {};
        allCourses.forEach(course => {
          // Academic year grouping
          if (!academicYearGroups[course.academicYear]) {
            academicYearGroups[course.academicYear] = 0;
          }
          academicYearGroups[course.academicYear]++;
          
          // Department grouping
          if (!departmentGroups[course.department]) {
            departmentGroups[course.department] = 0;
          }
          departmentGroups[course.department]++;
        });
        
        console.log("📅 [Service] Available academic years:", academicYearGroups);
        console.log("🏛️ [Service] Available departments:", departmentGroups);
        
        // Get all courses for this department and academic year from CourseOffering
        const courses = await CourseOffering.find({
          department: department,
          academicYear: academicYear,
          status: { $ne: "Completed" }
        }).select('courseCode courseName yearSem type department');

        console.log(`📚 [Service] Found ${courses.length} courses for department ${department} in ${academicYear}:`, courses);

        if (courses.length > 0) {
          console.log(`🔄 [Service] Creating credentials for ${courses.length} courses...`);
          const credentials = await this.createCourseCoordinatorCredentialsForCourses(
            courses.map(course => ({
              courseCode: course.courseCode,
              courseName: course.courseName,
              yearSem: course.yearSem,
              type: course.type,
              department: course.department
            })), 
            academicYear, 
            department,
            createdBy
          );

          console.log(`✅ [Service] Created ${credentials.length} credentials for department ${department}`);
          createdCredentials.push(...credentials);
        } else {
          console.log(`⚠️ [Service] No courses found for department ${department}`);
        }
      }

      console.log(`🎉 [Service] Total credentials created: ${createdCredentials.length}`);
      return createdCredentials;
    } catch (error) {
      console.error("❌ [Service] Error creating Course Coordinator credentials:", error);
      throw error;
    }
  },

  // Create Course Coordinator credentials for all courses
  async createCourseCoordinatorCredentialsForCourses(courses, academicYear, department, createdBy) {
    try {
      const existingUsernames = await User.find({
        role: "Course Coordinator",
      }).select("email");

      const existingUsernameList = existingUsernames.map(user => 
        user.email.split('@')[0] // Extract username part from email
      );

      const createdCredentials = [];

      for (const course of courses) {
        const { courseCode, courseName, yearSem } = course;
        
        // Check if Course Coordinator already exists for this specific course and academic year
        const existing = await User.findOne({
          role: "Course Coordinator",
          courseCode: courseCode,
          academicYear: academicYear,
          department: department || course.department
        });

        if (!existing) {
          // Also check if a user with the expected email already exists
          const username = await generateCourseCoordinatorUsername(courseCode, existingUsernameList);
          const expectedEmail = `${username}@somaiya.edu`;
          
          const emailExists = await User.findOne({ email: expectedEmail });
          
          if (!emailExists) {
            const password = generateSecurePassword();
            const hashedPassword = await bcrypt.hash(password, 10);

            const courseCoordinator = new User({
              name: "Unassigned", // Placeholder name for unassigned Course Coordinator
              email: expectedEmail,
              username: expectedEmail.split('@')[0], // Set username during creation
              role: "Course Coordinator",
              department: department || course.department,
              progDept: department || course.department,
              academicYear: academicYear,
              courseCode: courseCode,
              courseName: courseName,
              yearSem: yearSem,
              facultyID: await generateCCFacultyId(), // Clean format like CC0001
              passwordHash: hashedPassword,
              permissions: ["manage_course", "view_students", "manage_grades"],
            });

            await courseCoordinator.save();
            existingUsernameList.push(username);

            createdCredentials.push({
              username: username,
              password: password, // Return plain password for email sending
              courseCode: courseCode,
              courseName: courseName,
              department: department || course.department,
              academicYear: academicYear,
              userId: courseCoordinator._id,
            });
          } else {
            console.log(`User with email ${expectedEmail} already exists, skipping creation for ${courseCode}`);
          }
        } else {
          console.log(`Course Coordinator already exists for ${courseCode} in ${academicYear}:`, existing._id);
        }
      }

      return createdCredentials;
    } catch (error) {
      console.error("Error creating Course Coordinator credentials:", error);
      throw error;
    }
  },

  // Assign faculty to Course Coordinator role
  async assignCourseCoordinator(courseCode, courseName, academicYear, department, facultyId, assignedBy) {
    try {
      console.log(`Searching for Course Coordinator with parameters:`, {
        courseCode,
        courseName,
        academicYear,
        department,
        facultyId
      });

      // Find the specific Course Coordinator record for this course and academic year
      let courseCoordinatorRecord = await User.findOne({
        role: "Course Coordinator",
        courseCode: courseCode,
        academicYear: academicYear,
        department: department
      });

      if (!courseCoordinatorRecord) {
        console.log(`No Course Coordinator found for course: "${courseCode}", academicYear: "${academicYear}"`);
        
        // Let's search for Course Coordinators for this course to see what exists
        const courseCoordinatorRecords = await User.find({ 
          role: "Course Coordinator",
          courseCode: courseCode
        }).select('courseCode courseName department academicYear name email');
        console.log(`Course Coordinators found for course ${courseCode}:`, courseCoordinatorRecords);
        
        throw new Error(`Course Coordinator not found for course "${courseCode}" and academic year "${academicYear}"`);
      }

      console.log(`Found Course Coordinator for assignment:`, {
        id: courseCoordinatorRecord._id,
        courseCode: courseCoordinatorRecord.courseCode,
        courseName: courseCoordinatorRecord.courseName,
        department: courseCoordinatorRecord.department,
        academicYear: courseCoordinatorRecord.academicYear,
        email: courseCoordinatorRecord.email
      });

      // Find the faculty member to assign
      console.log(`Searching for faculty with facultyID: "${facultyId}"`);
      const faculty = await User.findOne({ facultyID: facultyId });
      if (!faculty) {
        console.log(`Faculty not found with facultyID: "${facultyId}"`);
        // Let's also search by other possible fields
        const facultyById = await User.findById(facultyId).catch(() => null);
        const facultyByEmail = await User.findOne({ email: facultyId }).catch(() => null);
        
        console.log('Faculty search results:', {
          byFacultyID: faculty,
          byId: facultyById,
          byEmail: facultyByEmail
        });
        
        throw new Error("Faculty member not found");
      }

      console.log(`Found faculty for assignment:`, {
        id: faculty._id,
        name: faculty.name,
        facultyID: faculty.facultyID,
        email: faculty.email
      });

      // Update the Course Coordinator record with faculty details
      courseCoordinatorRecord.name = faculty.name;
      courseCoordinatorRecord.mobileNumber = faculty.mobileNumber;
      courseCoordinatorRecord.dateOfBirth = faculty.dateOfBirth;

      // Generate username and new password for security when assigning
      const username = courseCoordinatorRecord.email.split('@')[0];
      courseCoordinatorRecord.username = username; // Set the username in the database

      const newPassword = generateSecurePassword();
      console.log("Generated new password for assignment:", newPassword ? "***GENERATED***" : "***FAILED***");
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      courseCoordinatorRecord.passwordHash = hashedPassword;

      await courseCoordinatorRecord.save();

      // Also update the CourseOffering to add this faculty as coordinator if not already present
      try {
        const courseOffering = await CourseOffering.findOne({
          courseCode: courseCode,
          academicYear: academicYear,
          department: department
        });

        if (courseOffering) {
          const isAlreadyCoordinator = courseOffering.courseCoordinators.some(
            coord => coord.facultyID === faculty.facultyID
          );

          if (!isAlreadyCoordinator) {
            courseOffering.courseCoordinators.push({
              facultyID: faculty.facultyID,
              name: faculty.name
            });
            await courseOffering.save();
            console.log(`Added faculty ${faculty.name} as coordinator to CourseOffering`);
          }
        }
      } catch (error) {
        console.log("Warning: Could not update CourseOffering:", error.message);
        // Continue execution even if CourseOffering update fails
      }

      const credentialsToReturn = {
        username: username,
        password: newPassword, // Return the new password for email sending
        email: courseCoordinatorRecord.email
      };

      console.log("Returning credentials:", {
        username: credentialsToReturn.username,
        password: credentialsToReturn.password ? "***PROVIDED***" : "***MISSING***",
        email: credentialsToReturn.email
      });

      return {
        courseCoordinator: courseCoordinatorRecord,
        assignedFaculty: faculty,
        credentials: credentialsToReturn
      };
    } catch (error) {
      console.error("Error assigning Course Coordinator:", error);
      throw error;
    }
  },

  // Reset password for Course Coordinator
  async resetCourseCoordinatorPassword(courseCoordinatorId) {
    try {
      const newPassword = generateSecurePassword();
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      const courseCoordinator = await User.findOneAndUpdate(
        { _id: courseCoordinatorId, role: "Course Coordinator" },
        { passwordHash: hashedPassword },
        { new: true }
      );

      if (!courseCoordinator) {
        throw new Error("Course Coordinator not found");
      }

      return {
        courseCoordinator,
        newPassword, // Return for email sending
      };
    } catch (error) {
      console.error("Error resetting Course Coordinator password:", error);
      throw error;
    }
  },

  // Get courses for a department and academic year (helper function)
  async getCoursesForCredentialCreation(department, academicYear) {
    try {
      const courses = await CourseOffering.find({
        department: department,
        academicYear: academicYear,
        status: { $ne: "Completed" }
      }).select('courseCode courseName yearSem type department');

      return courses.map(course => ({
        courseCode: course.courseCode,
        courseName: course.courseName,
        yearSem: course.yearSem,
        type: course.type,
        department: course.department
      }));
    } catch (error) {
      console.error("Error fetching courses for credential creation:", error);
      throw error;
    }
  },
};

export default courseCoordinatorService;
