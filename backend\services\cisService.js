import User from "../models/User.js";
import Department from "../models/Department.js";
import bcrypt from "bcryptjs";
import { generateCISFacultyId } from "../utils/facultyIdGenerator.js";

// Generate secure password
const generateSecurePassword = (length = 8) => {
  const minLength = Math.max(length, 8);
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const digits = "0123456789";
  const special = "!@#$%^&*()_+-=[]{}|;:,.<>?";
  const all = uppercase + lowercase + digits + special;

  const getRandom = (chars) => chars[Math.floor(Math.random() * chars.length)];

  let password = [
    getRandom(uppercase),
    getRandom(digits),
    getRandom(special),
    getRandom(lowercase),
  ];

  for (let i = password.length; i < minLength; i++) {
    password.push(getRandom(all));
  }

  return password.sort(() => Math.random() - 0.5).join("");
};

// Generate username for CIS coordinator using department abbreviation
const generateCISUsername = async (departmentName, existingUsernames = []) => {
  try {
    // Try to find department with abbreviation
    const department = await Department.findOne({ name: departmentName });
    
    let prefix;
    if (department && department.abbreviation) {
      prefix = department.abbreviation.toUpperCase();
    } else {
      // Fallback to first letters if no abbreviation
      prefix = departmentName
        .split(" ")
        .map((word) => word[0])
        .join("")
        .toUpperCase();
    }
    
    let base = `CIS${prefix}`;
    let final = base;
    let i = 1;
    while (existingUsernames && existingUsernames.includes(final.toLowerCase())) {
      final = `${base}${i++}`;
    }
    return final.toLowerCase(); // Store as lowercase but generate with uppercase
  } catch (error) {
    console.error("Error generating CIS username:", error);
    // Fallback to basic generation
    const prefix = departmentName
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase();
    let base = `CIS${prefix}`;
    let final = base;
    let i = 1;
    while (existingUsernames && existingUsernames.includes(final.toLowerCase())) {
      final = `${base}${i++}`;
    }
    return final.toLowerCase();
  }
};

export const cisService = {
  // Get all CIS coordinators for a specific academic year
  async getCISCoordinators(academicYear) {
    try {
      // Find all CIS coordinators for the specific academic year
      const cisCoordinators = await User.find({
        role: "CIS Coordinator",
        academicYear: academicYear
      }).select("name email department progDept academicYear facultyID");

      return cisCoordinators;
    } catch (error) {
      console.error("Error fetching CIS coordinators:", error);
      throw error;
    }
  },

  // Create CIS coordinator credentials for all departments
  async createCISCredentialsForDepartments(departments, academicYear, createdBy) {
    try {
      const existingUsernames = await User.find({
        role: "CIS Coordinator",
      }).select("email");

      const existingUsernameList = existingUsernames.map(user => 
        user.email.split('@')[0] // Extract username part from email
      );

      const createdCredentials = [];

      for (const department of departments) {
        // Check if CIS coordinator already exists for this specific department and academic year
        const existingByDept = await User.findOne({
          role: "CIS Coordinator",
          department: department,
          academicYear: academicYear
        });

        if (!existingByDept) {
          const username = await generateCISUsername(department, existingUsernameList);
          const email = `${username}@somaiya.edu`;
          
          // Also check if email already exists in the database
          const existingByEmail = await User.findOne({ email: email });
          
          if (!existingByEmail) {
            const password = generateSecurePassword();
            const hashedPassword = await bcrypt.hash(password, 10);

            const cisCoordinator = new User({
              name: "Unassigned", // Placeholder name for unassigned coordinator
              email: email,
              username: email.split('@')[0], // Set username during creation
              role: "CIS Coordinator",
              department: department,
              progDept: department,
              academicYear: academicYear,
              facultyID: await generateCISFacultyId(), // Clean format like CIS0001
              passwordHash: hashedPassword,
              permissions: ["manage_internal_assessment"],
            });

            try {
              await cisCoordinator.save();
              existingUsernameList.push(username);

              createdCredentials.push({
                username: username,
                password: password, // Return plain password for email sending
                department: department,
                academicYear: academicYear,
                userId: cisCoordinator._id,
              });
            } catch (saveError) {
              if (saveError.code === 11000) {
                console.log(`Duplicate key error for CIS coordinator ${email}, skipping creation for ${department}`);
              } else {
                console.error(`Error saving CIS coordinator for ${department}:`, saveError);
                throw saveError;
              }
            }
          } else {
            console.log(`User with email ${email} already exists, skipping creation for ${department}`);
          }
        } else {
          console.log(`CIS coordinator already exists for ${department} in ${academicYear}:`, existingByDept._id);
          console.log(`Found existing coordinator with department: "${existingByDept.department}", progDept: "${existingByDept.progDept}", academicYear: "${existingByDept.academicYear}"`);
        }
      }

      return createdCredentials;
    } catch (error) {
      console.error("Error creating CIS credentials:", error);
      throw error;
    }
  },

  // Assign faculty to CIS coordinator role
  async assignCISCoordinator(department, academicYear, facultyId, assignedBy) {
    try {
      console.log(`Searching for CIS coordinator with parameters:`, {
        department,
        academicYear,
        facultyId
      });

      // Generate the expected username for this department's CIS coordinator
      const expectedUsername = await generateCISUsername(department);
      console.log(`Looking for CIS coordinator with username: ${expectedUsername}`);

      // Find the specific CIS coordinator record for this department and academic year
      let cisRecord = await User.findOne({
        role: "CIS Coordinator",
        department: department,
        academicYear: academicYear
      });

      if (!cisRecord) {
        console.log(`No CIS coordinator found for department: "${department}", academicYear: "${academicYear}"`);
        console.log(`Expected username would be: ${expectedUsername}`);
        
        // Let's search for CIS coordinators for this department to see what exists
        const departmentCisRecords = await User.find({ 
          role: "CIS Coordinator",
          $or: [
            { department: department },
            { progDept: department }
          ]
        }).select('department progDept academicYear name email');
        console.log(`CIS coordinators found for department ${department}:`, departmentCisRecords);
        
        throw new Error(`CIS coordinator not found for department "${department}" and academic year "${academicYear}". Expected username: ${expectedUsername}`);
      }

      console.log(`Found CIS coordinator for assignment:`, {
        id: cisRecord._id,
        department: cisRecord.department,
        progDept: cisRecord.progDept,
        academicYear: cisRecord.academicYear,
        email: cisRecord.email
      });

      // Find the faculty member to assign
      console.log(`Searching for faculty with facultyID: "${facultyId}"`);
      const faculty = await User.findOne({ facultyID: facultyId });
      if (!faculty) {
        console.log(`Faculty not found with facultyID: "${facultyId}"`);
        // Let's also search by other possible fields
        const facultyById = await User.findById(facultyId).catch(() => null);
        const facultyByEmail = await User.findOne({ email: facultyId }).catch(() => null);
        
        console.log('Faculty search results:', {
          byFacultyID: faculty,
          byId: facultyById,
          byEmail: facultyByEmail
        });
        
        throw new Error("Faculty member not found");
      }

      console.log(`Found faculty for assignment:`, {
        id: faculty._id,
        name: faculty.name,
        facultyID: faculty.facultyID,
        email: faculty.email
      });

      // Update the CIS coordinator record with faculty details
      cisRecord.name = faculty.name;
      cisRecord.mobileNumber = faculty.mobileNumber;
      cisRecord.dateOfBirth = faculty.dateOfBirth;

      // Generate username and new password for security when assigning
      const username = cisRecord.email.split('@')[0];
      cisRecord.username = username; // Set the username in the database

      const newPassword = generateSecurePassword();
      console.log("Generated new password for assignment:", newPassword ? "***GENERATED***" : "***FAILED***");
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      cisRecord.passwordHash = hashedPassword;

      await cisRecord.save();

      const credentialsToReturn = {
        username: username,
        password: newPassword, // Return the new password for email sending
        email: cisRecord.email
      };

      console.log("Returning credentials:", {
        username: credentialsToReturn.username,
        password: credentialsToReturn.password ? "***PROVIDED***" : "***MISSING***",
        email: credentialsToReturn.email
      });

      return {
        cisCoordinator: cisRecord,
        assignedFaculty: faculty,
        credentials: credentialsToReturn
      };
    } catch (error) {
      console.error("Error assigning CIS coordinator:", error);
      throw error;
    }
  },

  // Reset password for CIS coordinator
  async resetCISPassword(cisCoordinatorId) {
    try {
      const newPassword = generateSecurePassword();
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      const cisCoordinator = await User.findOneAndUpdate(
        { _id: cisCoordinatorId, role: "CIS Coordinator" },
        { passwordHash: hashedPassword },
        { new: true }
      );

      if (!cisCoordinator) {
        throw new Error("CIS coordinator not found");
      }

      return {
        cisCoordinator,
        newPassword, // Return for email sending
      };
    } catch (error) {
      console.error("Error resetting CIS password:", error);
      throw error;
    }
  },
};

export default cisService;
