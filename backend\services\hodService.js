import User from "../models/User.js";
import Department from "../models/Department.js";
import bcrypt from "bcryptjs";
import { generateHODFacultyId } from "../utils/facultyIdGenerator.js";

// Generate secure password
const generateSecurePassword = (length = 8) => {
  const minLength = Math.max(length, 8);
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const digits = "0123456789";
  const special = "!@#$%^&*()_+-=[]{}|;:,.<>?";
  const all = uppercase + lowercase + digits + special;

  const getRandom = (chars) => chars[Math.floor(Math.random() * chars.length)];

  let password = [
    getRandom(uppercase),
    getRandom(digits),
    getRandom(special),
    getRandom(lowercase),
  ];

  for (let i = password.length; i < minLength; i++) {
    password.push(getRandom(all));
  }

  return password.sort(() => Math.random() - 0.5).join("");
};

// Generate username for HoD using department abbreviation
const generateHoDUsername = async (departmentName, existingUsernames = []) => {
  try {
    // Try to find department with abbreviation
    const department = await Department.findOne({ name: departmentName });
    
    let prefix;
    if (department && department.abbreviation) {
      prefix = department.abbreviation.toUpperCase();
    } else {
      // Fallback to first letters if no abbreviation
      prefix = departmentName
        .split(" ")
        .map((word) => word[0])
        .join("")
        .toUpperCase();
    }
    
    let base = `HOD${prefix}`;
    let final = base;
    let i = 1;
    while (existingUsernames && existingUsernames.includes(final.toLowerCase())) {
      final = `${base}${i++}`;
    }
    return final.toLowerCase(); // Store as lowercase but generate with uppercase
  } catch (error) {
    console.error("Error generating HoD username:", error);
    // Fallback to basic generation
    const prefix = departmentName
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase();
    let base = `HOD${prefix}`;
    let final = base;
    let i = 1;
    while (existingUsernames && existingUsernames.includes(final.toLowerCase())) {
      final = `${base}${i++}`;
    }
    return final.toLowerCase();
  }
};

export const hodService = {
  // Get all HoDs for a specific academic year
  async getHoDs(academicYear) {
    try {
      // Find all HoDs for the specific academic year
      const hods = await User.find({
        role: "HOD",
        academicYear: academicYear
      }).select("name email department progDept academicYear facultyID");

      return hods;
    } catch (error) {
      console.error("Error fetching HoDs:", error);
      throw error;
    }
  },

  // Create HoD credentials for all departments
  async createHoDCredentialsForDepartments(departments, academicYear, createdBy) {
    try {
      const existingUsernames = await User.find({
        role: "HOD",
      }).select("email");

      const existingUsernameList = existingUsernames.map(user => 
        user.email.split('@')[0] // Extract username part from email
      );

      const createdCredentials = [];

      for (const department of departments) {
        // Check if HoD already exists for this specific department and academic year
        const existing = await User.findOne({
          role: "HOD",
          department: department,
          academicYear: academicYear
        });

        if (!existing) {
          // Also check if a user with the expected email already exists
          const username = await generateHoDUsername(department, existingUsernameList);
          const expectedEmail = `${username}@somaiya.edu`;
          
          const emailExists = await User.findOne({ email: expectedEmail });
          
          if (!emailExists) {
            const password = generateSecurePassword();
            const hashedPassword = await bcrypt.hash(password, 10);

            const hod = new User({
              name: "Unassigned", // Placeholder name for unassigned HoD
              email: expectedEmail,
              username: expectedEmail.split('@')[0], // Set username during creation
              role: "HOD",
              department: department,
              progDept: department,
              academicYear: academicYear,
              facultyID: await generateHODFacultyId(), // Clean format like HOD0001
              passwordHash: hashedPassword,
              permissions: ["manage_department", "view_reports"],
            });

            try {
              await hod.save();
              existingUsernameList.push(username);

              createdCredentials.push({
                username: username,
                password: password, // Return plain password for email sending
                department: department,
                academicYear: academicYear,
                userId: hod._id,
              });
            } catch (saveError) {
              if (saveError.code === 11000) {
                console.log(`Duplicate key error for HoD ${expectedEmail}, skipping creation for ${department}`);
              } else {
                console.error(`Error saving HoD for ${department}:`, saveError);
                throw saveError;
              }
            }
          } else {
            console.log(`User with email ${expectedEmail} already exists, skipping creation for ${department}`);
          }
        } else {
          console.log(`HoD already exists for ${department} in ${academicYear}:`, existing._id);
          console.log(`Found existing HoD with department: "${existing.department}", progDept: "${existing.progDept}", academicYear: "${existing.academicYear}"`);
        }
      }

      return createdCredentials;
    } catch (error) {
      console.error("Error creating HoD credentials:", error);
      throw error;
    }
  },

  // Assign faculty to HoD role
  async assignHoD(department, academicYear, facultyId, assignedBy) {
    try {
      console.log(`Searching for HoD with parameters:`, {
        department,
        academicYear,
        facultyId
      });

      // Generate the expected username for this department's HoD
      const expectedUsername = await generateHoDUsername(department);
      console.log(`Looking for HoD with username: ${expectedUsername}`);

      // Find the specific HoD record for this department and academic year
      let hodRecord = await User.findOne({
        role: "HOD",
        department: department,
        academicYear: academicYear
      });

      if (!hodRecord) {
        console.log(`No HoD found for department: "${department}", academicYear: "${academicYear}"`);
        console.log(`Expected username would be: ${expectedUsername}`);
        
        // Let's search for HoDs for this department to see what exists
        const departmentHodRecords = await User.find({ 
          role: "HOD",
          $or: [
            { department: department },
            { progDept: department }
          ]
        }).select('department progDept academicYear name email');
        console.log(`HoDs found for department ${department}:`, departmentHodRecords);
        
        throw new Error(`HoD not found for department "${department}" and academic year "${academicYear}". Expected username: ${expectedUsername}`);
      }

      console.log(`Found HoD for assignment:`, {
        id: hodRecord._id,
        department: hodRecord.department,
        progDept: hodRecord.progDept,
        academicYear: hodRecord.academicYear,
        email: hodRecord.email
      });

      // Find the faculty member to assign
      console.log(`Searching for faculty with facultyID: "${facultyId}"`);
      const faculty = await User.findOne({ facultyID: facultyId });
      if (!faculty) {
        console.log(`Faculty not found with facultyID: "${facultyId}"`);
        // Let's also search by other possible fields
        const facultyById = await User.findById(facultyId).catch(() => null);
        const facultyByEmail = await User.findOne({ email: facultyId }).catch(() => null);
        
        console.log('Faculty search results:', {
          byFacultyID: faculty,
          byId: facultyById,
          byEmail: facultyByEmail
        });
        
        throw new Error("Faculty member not found");
      }

      console.log(`Found faculty for assignment:`, {
        id: faculty._id,
        name: faculty.name,
        facultyID: faculty.facultyID,
        email: faculty.email
      });

      // Update the HoD record with faculty details
      hodRecord.name = faculty.name;
      hodRecord.mobileNumber = faculty.mobileNumber;
      hodRecord.dateOfBirth = faculty.dateOfBirth;

      // Generate username and new password for security when assigning
      const username = hodRecord.email.split('@')[0];
      hodRecord.username = username; // Set the username in the database

      const newPassword = generateSecurePassword();
      console.log("Generated new password for assignment:", newPassword ? "***GENERATED***" : "***FAILED***");
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      hodRecord.passwordHash = hashedPassword;

      await hodRecord.save();

      const credentialsToReturn = {
        username: username,
        password: newPassword, // Return the new password for email sending
        email: hodRecord.email
      };

      console.log("Returning credentials:", {
        username: credentialsToReturn.username,
        password: credentialsToReturn.password ? "***PROVIDED***" : "***MISSING***",
        email: credentialsToReturn.email
      });

      return {
        hod: hodRecord,
        assignedFaculty: faculty,
        credentials: credentialsToReturn
      };
    } catch (error) {
      console.error("Error assigning HoD:", error);
      throw error;
    }
  },

  // Reset password for HoD
  async resetHoDPassword(hodId) {
    try {
      const newPassword = generateSecurePassword();
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      const hod = await User.findOneAndUpdate(
        { _id: hodId, role: "HOD" },
        { passwordHash: hashedPassword },
        { new: true }
      );

      if (!hod) {
        throw new Error("HoD not found");
      }

      return {
        hod,
        newPassword, // Return for email sending
      };
    } catch (error) {
      console.error("Error resetting HoD password:", error);
      throw error;
    }
  },
};

export default hodService;
