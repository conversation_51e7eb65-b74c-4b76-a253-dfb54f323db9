import nodemailer from "nodemailer";

export const sendVerificationCode = async (req, res) => {
  const { email, code } = req.body;

  if (!email || !code) {
    return res.status(400).json({ message: "Email and code are required." });
  }

  try {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Verification Code",
      text: `Your verification code is: ${code}`,
    };

    await transporter.sendMail(mailOptions);

    res.status(200).json({ message: "Verification code sent successfully." });
  } catch (error) {
    console.error("Error sending email:", error);
    res.status(500).json({ message: "Failed to send verification code." });
  }
};

export const sendCredentials = async (req, res) => {
  const { email, name, username, password, role, department } = req.body;

  console.log("Received credentials email request:", {
    email,
    name,
    username,
    password: password ? "***PROVIDED***" : "***MISSING***",
    role,
    department
  });

  if (!email || !name || !username || !password || !role) {
    console.log("Missing required fields:", {
      email: !!email,
      name: !!name,
      username: !!username,
      password: !!password,
      role: !!role
    });
    return res.status(400).json({ message: "Email, name, username, password, and role are required." });
  }

  try {
    // Check if email configuration is available
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
      console.error("Email configuration missing:", {
        EMAIL_USER: !!process.env.EMAIL_USER,
        EMAIL_PASSWORD: !!process.env.EMAIL_PASSWORD
      });
      return res.status(500).json({ message: "Email service not configured." });
    }

    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
      timeout: 10000, // 10 second timeout
      connectionTimeout: 10000, // 10 second connection timeout
    });

    const roleDisplayNames = {
      cis: "CIS Coordinator",
      cc: "Course Coordinator", 
      hod: "Head of Department",
      faculty: "Faculty",
      eic: "Exam Cell (EIC)",
      principal: "Principal"
    };

    const roleDisplay = roleDisplayNames[role] || role;
    const departmentText = department ? ` for ${department} department` : '';

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: `Your ${roleDisplay} Account Credentials`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h2 style="color: #ED1C24; margin: 0;">K.J. Somaiya College of Engineering</h2>
            <p style="color: #666; margin: 5px 0 0 0;">Continuous Internal Systems</p>
          </div>
          
          <div style="background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
            <h3 style="color: #333; margin: 0 0 15px 0;">Account Assignment Notification</h3>
            <p style="margin: 0 0 10px 0;">Dear <strong>${name}</strong>,</p>
            <p style="margin: 0 0 15px 0;">You have been assigned as <strong>${roleDisplay}</strong>${departmentText}. Below are your login credentials:</p>
          </div>
          
          <div style="background-color: #fff; border: 2px solid #ED1C24; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
            <h4 style="color: #ED1C24; margin: 0 0 15px 0;">Login Credentials</h4>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Username:</td>
                <td style="padding: 8px 0; color: #666;">${username}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Password:</td>
                <td style="padding: 8px 0; color: #666; font-family: monospace; background-color: #f5f5f5; padding: 4px 8px; border-radius: 3px;">${password}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #333;">Role:</td>
                <td style="padding: 8px 0; color: #666;">${roleDisplay}</td>
              </tr>
              ${department ? `<tr><td style="padding: 8px 0; font-weight: bold; color: #333;">Department:</td><td style="padding: 8px 0; color: #666;">${department}</td></tr>` : ''}
            </table>
          </div>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <p style="margin: 0; color: #856404;"><strong>Important Security Notice:</strong></p>
            <ul style="margin: 10px 0 0 0; color: #856404;">
              <li>Please change your password after first login</li>
              <li>Do not share your credentials with anyone</li>
              <li>Keep your login information secure</li>
            </ul>
          </div>
          
          <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 14px; margin: 0;">
              If you have any questions, please contact the system administrator.
            </p>
            <p style="color: #999; font-size: 12px; margin: 10px 0 0 0;">
              This is an automated message. Please do not reply to this email.
            </p>
          </div>
        </div>
      `,
    };

    console.log(`Attempting to send email to ${email}...`);
    await transporter.sendMail(mailOptions);
    console.log(`Credentials email sent successfully to ${email}`);

    res.status(200).json({ message: "Credentials sent successfully." });
  } catch (error) {
    console.error("Error sending credentials email:", error);
    res.status(500).json({ message: "Failed to send credentials email." });
  }
};
