import { useState, useEffect } from "react";
import Navbar from "../../common_components/Navbar";
import Sidebar from "../../common_components/Sidebar";
import PasswordModal from "../../common_components/PasswordModal";
import defaultProfilePic from "../../assets/logo.png";
import { profileService, UserProfile } from "../../services/profileService";

const CISMyProfile = () => {
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [profileData, setProfileData] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        const data = await profileService.getMyProfile();
        setProfileData(data);
        setError(null);
      } catch (err) {
        console.error("Error fetching profile:", err);
        setError(err instanceof Error ? err.message : "Failed to load profile");
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  if (loading) {
    return (
      <div className="flex flex-col h-screen">
        <Navbar role="ciscoordinator" />
        <div className="flex flex-1 overflow-hidden">
          <div className="hidden md:block">
            <Sidebar role="ciscoordinator" />
          </div>
          <div className="flex-1 p-6 bg-gray-50 overflow-auto flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#ED1C24] mx-auto mb-4"></div>
              <p className="text-gray-600">Loading profile...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-screen">
        <Navbar role="ciscoordinator" />
        <div className="flex flex-1 overflow-hidden">
          <div className="hidden md:block">
            <Sidebar role="ciscoordinator" />
          </div>
          <div className="flex-1 p-6 bg-gray-50 overflow-auto flex items-center justify-center">
            <div className="text-center">
              <div className="text-red-500 text-xl mb-4">⚠️</div>
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-[#ED1C24] text-white rounded hover:bg-red-700 transition"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="flex flex-col h-screen">
        <Navbar role="ciscoordinator" />
        <div className="flex flex-1 overflow-hidden">
          <div className="hidden md:block">
            <Sidebar role="ciscoordinator" />
          </div>
          <div className="flex-1 p-6 bg-gray-50 overflow-auto flex items-center justify-center">
            <p className="text-gray-600">No profile data available</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
      <Navbar role="ciscoordinator" />
      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar role="ciscoordinator" />
        </div>

        <div className="flex-1 p-6 bg-gray-50 overflow-auto">
          <h1 className="text-[30px] font-bold text-[#ED1C24] mb-10 text-center">My Profile</h1>

          <div className="bg-white rounded-lg shadow p-7 relative">
            <div className="flex items-start justify-between flex-wrap gap-4">
              <div className="flex gap-4 items-center">
                <img src={defaultProfilePic} alt="Profile" className="w-16 h-16 rounded-full" />
                <div>
                  <h2 className="text-xl font-semibold mb-1">{profileData.name}</h2>
                  <p className="text-sm text-gray-600">Department: {profileData.progDept || profileData.department || 'N/A'}</p>
                </div>
              </div>

              <div className="text-sm text-gray-600 text-right font-medium">
                Academic Year: <span className="text-[#ED1C24]"> {profileData.academicYear || 'N/A'}</span>
              </div>
            </div>

            <div className="text-sm pt-6 space-y-2">
              <p>
                <strong>Role:</strong> {profileData.role}
              </p>
              <p>
                <strong>DOB:</strong> {profileData.dateOfBirth ? new Date(profileData.dateOfBirth).toLocaleDateString() : 'N/A'}
              </p>
              <p>
                <strong>Phone:</strong> {profileData.mobileNumber || 'N/A'}
              </p>
              <p>
                <strong>Email ID:</strong> {profileData.email}
              </p>
            </div>

            {/* Change Password Button */}
            <div className="flex justify-end">
              <button
                onClick={() => setShowPasswordModal(true)}
                className="px-4 py-2 bg-red-700 text-white rounded hover:bg-red-800 transition text-sm font-medium mt-2"
              >
                Change Password
              </button>
            </div>
          </div>
        </div>
      </div>

      <PasswordModal show={showPasswordModal} onClose={() => setShowPasswordModal(false)} />
    </div>
  );
};

export default CISMyProfile;
