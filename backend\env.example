# Database Configuration
MONGO_URI=mongodb://localhost:27017/cis_swdc
# For production: mongodb+srv://username:<EMAIL>/database

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production

# Server Configuration
PORT=7000
NODE_ENV=development

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# Email Configuration (Gmail)
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password-here

# Default Admin Configuration (optional - will use defaults if not set)
DEFAULT_ADMIN_NAME=System Administrator
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=ChangeMe123!
DEFAULT_ADMIN_FACULTY_ID=ADMIN001
DEFAULT_ADMIN_DEPARTMENT=Administration
DEFAULT_ADMIN_PROG_DEPT=Administration

# Security Notes:
# 1. Never commit .env files to version control
# 2. Use strong, unique passwords for production
# 3. Enable 2FA on Gmail and use App Passwords
# 4. Use environment-specific .env files (.env.development, .env.production)
# 5. Rotate JWT secrets regularly in production
