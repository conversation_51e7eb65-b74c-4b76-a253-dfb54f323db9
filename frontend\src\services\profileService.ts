import axios from 'axios';

// Configure axios defaults
const apiClient = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:7000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Types
export interface UserProfile {
  _id: string;
  name: string;
  username?: string;
  email: string;
  mobileNumber?: string;
  dateOfBirth?: Date;
  role: string;
  department?: string;
  progDept?: string;
  academicYear?: string;
  currentYearSem?: string;
  facultyID?: string;
  rollNumber?: string;
  div?: string;
  batch?: string;
  courseCode?: string;
  courseName?: string;
  yearSem?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  enrolledTheoryCourses?: Array<{
    _id: string;
    courseCode: string;
    courseName: string;
  }>;
  enrolledLabCourses?: Array<{
    _id: string;
    courseCode: string;
    courseName: string;
  }>;
}

export interface UpdateProfileData {
  name?: string;
  email?: string;
  mobileNumber?: string;
  dateOfBirth?: string; // DD-MM-YYYY format
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

// Profile Service
export const profileService = {
  // Get current user profile
  getMyProfile: async (): Promise<UserProfile> => {
    try {
      const response = await apiClient.get<ApiResponse<UserProfile>>('/profile/me');
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Failed to fetch profile');
    } catch (error: any) {
      console.error('Error fetching profile:', error);
      throw new Error(error.response?.data?.message || error.message || 'Failed to fetch profile');
    }
  },

  // Update user profile
  updateProfile: async (data: UpdateProfileData): Promise<UserProfile> => {
    try {
      const response = await apiClient.put<ApiResponse<UserProfile>>('/profile/update', data);
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Failed to update profile');
    } catch (error: any) {
      console.error('Error updating profile:', error);
      throw new Error(error.response?.data?.message || error.message || 'Failed to update profile');
    }
  },

  // Change password
  changePassword: async (data: ChangePasswordData): Promise<void> => {
    try {
      const response = await apiClient.put<ApiResponse<void>>('/profile/change-password', data);
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to change password');
      }
    } catch (error: any) {
      console.error('Error changing password:', error);
      throw new Error(error.response?.data?.message || error.message || 'Failed to change password');
    }
  },
};

export default profileService;
