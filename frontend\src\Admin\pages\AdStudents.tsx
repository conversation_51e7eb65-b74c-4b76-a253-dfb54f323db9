import React, { useState, useEffect } from "react";
import Navbar from "../../common_components/Navbar";
import Sidebar from "../../common_components/Sidebar";
import DepartmentSelector from "../../common_components/DepartmentSelector";
import studentService, { Student } from "../../services/studentService";
import CSVImportModal from "../components/CSVImportModal";
import CourseMultiSelect from "../components/CourseMultiSelect";
import StudentGradesheetIntegration from "../components/StudentGradesheetIntegration";
import { useDepartments } from "../../hooks/useDepartments";
import { canEditStudent, getAcademicYearOptions } from "../../utils/academicYearUtils";

const academicYear = getAcademicYearOptions();

const yearSem = [
  "FY - I",
  "FY - II",
  "SY - III",
  "SY - IV",
  "TY - V",
  "TY - VI",
  "LY - VII",
  "LY - VIII",
];

const departmentYearSemDivisions: Record<string, Record<string, string[]>> = {
  "Computer Engineering": {
    "FY - I": ["Div A", "Div B", "Div C", "Div D", "Div E", "Div F"],
    "FY - II": ["Div A", "Div B", "Div C", "Div D", "Div E", "Div F"],
    "SY - III": ["Div A", "Div B", "Div C", "Div D", "Div E", "Div F"],
    "SY - IV": ["Div A", "Div B", "Div C", "Div D", "Div E", "Div F"],
    "TY - V": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "TY - VI": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "LY - VII": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "LY - VIII": ["Div A", "Sem Long Internship"],
  },
  "Information Technology": {
    "FY - I": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "FY - II": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "SY - III": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "SY - IV": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "TY - V": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "TY - VI": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "LY - VII": ["Div A", "Div B", "Div C", "Div D", "Div E"],
    "LY - VIII": ["Div A", "Sem Long Internship"],
  },
};

// Helper function to extract course IDs from course objects or strings
const extractCourseIds = (courses: (string | any)[]): string[] => {
  if (!courses) return [];
  return courses.map((course) => {
    if (typeof course === "string") return course;
    return course._id || course.courseCode || course;
  });
};

const AdStudents: React.FC = () => {
  // Department and academic year management
  const { departments } = useDepartments();
  const [selectedDept, setSelectedDept] = useState("");
  const [selectedAcademicYear, setSelectedAcademicYear] = useState(academicYear[0]);
  const [selectedYearSem, setSelectedYearSem] = useState("");
  const [availableYearSems, setAvailableYearSems] = useState<string[]>([]);

  // Student management states
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Division and batch states
  const [availableDivisions, setAvailableDivisions] = useState<string[]>([]);
  const [activeDivision, setActiveDivision] = useState("");

  // UI states
  const [searchQuery, setSearchQuery] = useState("");
  const [showCSVModal, setShowCSVModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);

  // Add student form states
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [rollNo, setRollNo] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [newStudentDiv, setNewStudentDiv] = useState("");
  const [batch, setBatch] = useState("");
  const [availableBatches, setAvailableBatches] = useState<string[]>([]);
  const [theoryCourses, setTheoryCourses] = useState<string[]>([]);
  const [labCourses, setLabCourses] = useState<string[]>([]);

  // Edit student states
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [showSaveConfirmModal, setShowSaveConfirmModal] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);

  // Initialize departments
  useEffect(() => {
    if (departments.length > 0 && !selectedDept) {
      setSelectedDept(departments[0]);
    }
  }, [departments, selectedDept]);

  // Fetch year semesters when department changes
  useEffect(() => {
    const fetchYearSemesters = async () => {
      if (!selectedDept) return;
      
      try {
        const result = await studentService.getAvailableYearSemesters();
        if (result.success) {
          setAvailableYearSems(result.data);
          if (result.data.length > 0 && !selectedYearSem) {
            setSelectedYearSem(result.data[0]);
          }
        }
      } catch (error) {
        console.error("Error fetching year semesters:", error);
      }
    };

    fetchYearSemesters();
  }, [selectedDept, selectedAcademicYear, selectedYearSem]);

  // Fetch divisions when department and year-sem change
  useEffect(() => {
    const fetchDivisions = async () => {
      if (!selectedDept || !selectedYearSem) return;
      
      try {
        const result = await studentService.getAvailableDivisions(selectedDept, selectedYearSem, selectedAcademicYear);
        if (result.success) {
          setAvailableDivisions(result.data);
          if (result.data.length > 0 && !activeDivision) {
            setActiveDivision(result.data[0]);
          }
        }
      } catch (error) {
        console.error("Error fetching divisions:", error);
      }
    };

    fetchDivisions();
  }, [selectedDept, selectedYearSem, selectedAcademicYear, activeDivision]);

  // Fetch students when filters change
  useEffect(() => {
    fetchStudents();
  }, [selectedDept, selectedAcademicYear, selectedYearSem, activeDivision]);

  const fetchStudents = async () => {
    if (!selectedDept || !selectedAcademicYear || !selectedYearSem) return;
    
    try {
      setLoading(true);
      setError(null);
      const result = await studentService.getStudents({
        department: selectedDept,
        academicYear: selectedAcademicYear,
        currentYearSem: selectedYearSem,
        div: activeDivision || undefined,
      });
      
      if (result.success) {
        setStudents(result.data);
      } else {
        setError("Failed to fetch students");
      }
    } catch (error: any) {
      console.error("Error fetching students:", error);
      setError(error.message || "Failed to fetch students");
    } finally {
      setLoading(false);
    }
  };

  // Filter students based on search query
  const filteredStudents = students.filter(
    (student) =>
      student.rollNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group students by batch
  const groupedStudents = filteredStudents.reduce((acc, student) => {
    const batch = student.batch || "No Batch";
    const existingGroup = acc.find(group => group.batch === batch);
    if (existingGroup) {
      existingGroup.students.push(student);
    } else {
      acc.push({ batch, students: [student] });
    }
    return acc;
  }, [] as { batch: string; students: Student[] }[]);

  const fetchAvailableBatches = async (division: string) => {
    if (!selectedDept || !selectedYearSem || !division) return;

    try {
      const result = await studentService.getAvailableBatches(selectedDept, selectedYearSem, division, selectedAcademicYear);
      if (result.success) {
        setAvailableBatches(result.data);
      }
    } catch (error) {
      console.error("Error fetching batches:", error);
    }
  };

  const handleAddStudent = async () => {
    try {
      setLoading(true);
      if (!name || !email || !selectedDept || !selectedAcademicYear || !selectedYearSem || !rollNo) {
        alert("Please fill all required fields (Name, Email, Department, Academic Year, Year-Sem, Roll No)");
        return;
      }

      // Ensure division is selected
      if (!newStudentDiv) {
        alert("Please select a division");
        return;
      }

      // Debug: Log form values
      console.log("Form values:", {
        name,
        email,
        rollNo,
        mobileNumber,
        selectedDept,
        selectedAcademicYear,
        selectedYearSem,
        newStudentDiv,
        batch,
        theoryCourses,
        labCourses
      });

      const newStudentData = {
        name,
        email,
        rollNumber: rollNo,
        mobileNumber: mobileNumber || "", // Send empty string instead of undefined
        progDept: selectedDept,
        academicYear: selectedAcademicYear,
        currentYearSem: selectedYearSem,
        div: newStudentDiv,
        batch: batch || "", // Send empty string instead of undefined
        enrolledTheoryCourses: theoryCourses.length > 0 ? theoryCourses : [],
        enrolledLabCourses: labCourses.length > 0 ? labCourses : [],
      };

      console.log("Sending student data:", newStudentData);
      const result = await studentService.createStudent(newStudentData);
      if (result.success) {
        alert("Student added successfully!");
        setShowAddModal(false);
        // Reset form
        setName("");
        setEmail("");
        setRollNo("");
        setMobileNumber("");
        setBatch("");
        setTheoryCourses([]);
        setLabCourses([]);
        // Refresh students list
        fetchStudents();
      } else {
        alert("Failed to add student");
      }
    } catch (error: any) {
      console.error("Error adding student:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to add student";
      alert(`Error adding student: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStudent = async () => {
    if (!editingStudent || !editingStudent._id) return;

    try {
      setLoading(true);
      const updateData = {
        name: editingStudent.name,
        email: editingStudent.email,
        rollNumber: editingStudent.rollNumber,
        mobileNumber: editingStudent.mobileNumber,
        div: editingStudent.div,
        batch: editingStudent.batch,
        enrolledTheoryCourses: editingStudent.enrolledTheoryCourses,
        enrolledLabCourses: editingStudent.enrolledLabCourses,
      };

      const response = await studentService.updateStudent(editingStudent._id, updateData);

      setShowEditModal(false);
      setShowSaveConfirmModal(false);
      setEditingStudent(null);

      // Refresh the student list
      await fetchStudents();

      // Show success message
      alert("Student updated successfully! Gradesheets have been updated automatically.");
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to update student");
      console.error("Error updating student:", err);
      alert("Error updating student: " + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteStudent = async (student?: Student) => {
    const studentToDelete = student || editingStudent;
    if (!studentToDelete || !studentToDelete._id) return;

    if (!window.confirm("Are you sure you want to delete this student?")) {
      return;
    }

    try {
      setLoading(true);
      const result = await studentService.deleteStudent(studentToDelete._id);
      if (result.success) {
        alert("Student deleted successfully!");
        if (showEditModal) {
          setShowEditModal(false);
          setShowDeleteConfirmModal(false);
          setEditingStudent(null);
        }
        fetchStudents();
      } else {
        alert("Failed to delete student");
      }
    } catch (error: any) {
      console.error("Error deleting student:", error);
      alert(error.message || "Failed to delete student");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <Navbar role="admin" />
      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar role="admin" />
        </div>
        <main className="flex-1 py-6 px-5 md:px-7 bg-white overflow-auto">
          <h1 className="text-[27px] font-bold text-[#ED1C24] text-center mb-7">
            Students
          </h1>
          
          {/* Filters */}
          <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
            <div className="flex flex-col md:flex-row gap-4 flex-1">
              {/* Department */}
              <div className="flex flex-col w-full md:w-[370px]">
                <select
                  className="border px-4 py-1.5 rounded w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                  value={selectedDept}
                  onChange={(e) => setSelectedDept(e.target.value)}
                >
                  <option value="">Select Department</option>
                  {departments.map((dept, index) => (
                    <option key={index} value={dept}>{dept}</option>
                  ))}
                </select>
              </div>

              {/* Year - Sem */}
              <div className="flex flex-col w-full md:w-[130px]">
                <label className="text-[16px] font-medium mb-1">Year - Sem:</label>
                <select
                  className="border px-4 py-1.5 rounded w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                  value={selectedYearSem}
                  onChange={(e) => setSelectedYearSem(e.target.value)}
                >
                  <option value="">Select Year-Sem</option>
                  {availableYearSems.map((ys, index) => (
                    <option key={index} value={ys}>{ys}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Academic Year */}
            <div className="flex flex-col items-end gap-2 w-full md:w-auto">
              <div className="flex flex-col w-full md:w-[160px]">
                <label className="text-[16px] font-medium mb-1">Academic Year:</label>
                <select
                  className="border px-4 py-1.5 rounded w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                  value={selectedAcademicYear}
                  onChange={(e) => setSelectedAcademicYear(e.target.value)}
                >
                  {academicYear.map((ay, index) => (
                    <option key={index} value={ay}>{ay}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          
          <hr className="mt-4 border-t border-2 border-gray-300" />
          
          {/* Search and Actions */}
          <div className="flex flex-col md:flex-row justify-between items-end mb-10 gap-4">
            <div className="flex flex-col w-full md:w-[500px] md:mt-7">
              <input
                type="text"
                className="border px-4 py-1.5 rounded-xl w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                placeholder="Search by Student Roll Number or Name"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setShowCSVModal(true)}
                className="bg-[#28a745] text-white px-4 py-2 rounded-xl hover:bg-[#218838]"
                disabled={loading}
              >
                Import CSV
              </button>
              <button
                onClick={async () => {
                  // Validate that required dropdowns are selected
                  if (!selectedDept) {
                    alert("Please select a department first");
                    return;
                  }
                  if (!selectedAcademicYear) {
                    alert("Please select an academic year first");
                    return;
                  }
                  if (!selectedYearSem) {
                    alert("Please select a year-semester first");
                    return;
                  }

                  // Initialize form with default values
                  console.log("Available divisions:", availableDivisions);
                  const defaultDivision = availableDivisions[0] || "Div A";
                  console.log("Setting default division:", defaultDivision);
                  setNewStudentDiv(defaultDivision);
                  setBatch("");
                  setRollNo("");
                  setName("");
                  setEmail("");
                  setMobileNumber("");
                  setTheoryCourses([]);
                  setLabCourses([]);
                  setShowAddModal(true);

                  // Fetch batches for the selected division
                  await fetchAvailableBatches(defaultDivision);
                }}
                className="bg-[#ED1C24] text-white px-4 py-2 rounded-xl hover:bg-red-700"
                disabled={loading}
              >
                Add Student
              </button>
            </div>
          </div>

          {/* Divisions */}
          {availableDivisions.length > 0 && (
            <div className="border-b border-gray-300 mb-6 shadow-[0_2px_3px_rgba(0,0,0,0.15)]">
              <div className="flex">
                {availableDivisions.map((division) => (
                  <div
                    key={division}
                    className={`px-12 py-2 cursor-pointer text-[16px] ${activeDivision === division
                      ? 'font-bold text-[#ED1C24] border-[#ED1C24] border-b-2 border-l border-t border-r rounded-t-lg'
                      : 'font-medium text-gray-700'
                      }`}
                    onClick={() => setActiveDivision(division)}
                  >
                    {division}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Students table */}
          <div className="overflow-auto">
            <table className="min-w-full border text-left text-sm">
              <thead className="bg-gray-100">
                <tr>
                  <th className="px-4 py-2 border-r text-center">Sr.No</th>
                  <th className="px-4 py-2 border-r text-center">Batch</th>
                  <th className="px-4 py-2 border-r text-center">Roll No</th>
                  <th className="px-4 py-2 border-r text-center">Name</th>
                  <th className="px-4 py-2 border-r text-center">Theory Courses</th>
                  <th className="px-4 py-2 border-r text-center">Lab Courses</th>
                  <th className="px-4 py-2 text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={5} className="text-center text-gray-500 py-4">
                      Loading students...
                    </td>
                  </tr>
                ) : groupedStudents.length > 0 ? (
                  groupedStudents.map((group) =>
                    group.students.map((student, idxInGroup) => {
                      const srNo = filteredStudents.findIndex(s => s._id === student._id) + 1;
                      return (
                        <tr key={student._id} className="border-t">
                          <td className="px-4 py-2 border-r text-center">{srNo}</td>
                          {idxInGroup === 0 && (
                            <td
                              className="px-4 py-2 border-r text-center"
                              rowSpan={group.students.length}
                            >
                              {student.batch || "No Batch"}
                            </td>
                          )}
                          <td className="px-4 py-2 border-r text-center">{student.rollNumber}</td>
                          <td className="px-4 py-2 border-r text-center">
                            <div className="flex items-center justify-center gap-2">
                              <span>{student.name}</span>
                              {!canEditStudent(student) && (
                                <span
                                  className="inline-block w-2 h-2 bg-orange-400 rounded-full"
                                  title="Previous academic year student - editing restricted"
                                />
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-2 border-r text-center">
                            <span className="text-xs text-gray-600">
                              {Array.isArray(student.enrolledTheoryCourses)
                                ? student.enrolledTheoryCourses.length
                                : 0}{" "}
                              courses
                            </span>
                          </td>
                          <td className="px-4 py-2 border-r text-center">
                            <span className="text-xs text-gray-600">
                              {Array.isArray(student.enrolledLabCourses)
                                ? student.enrolledLabCourses.length
                                : 0}{" "}
                              courses
                            </span>
                          </td>
                          <td className="px-4 py-2 text-center">
                            <div className="flex gap-2 justify-center">
                              <button
                                className="text-blue-600 font-medium hover:underline text-sm"
                                onClick={() => {
                                  setSelectedStudent(student);
                                  setShowDetailModal(true);
                                }}
                              >
                                View
                              </button>
                              <button
                                className={`font-medium hover:underline text-sm ${
                                  canEditStudent(student)
                                    ? "text-red-600 cursor-pointer"
                                    : "text-gray-400 cursor-not-allowed"
                                }`}
                                onClick={() => {
                                  if (canEditStudent(student)) {
                                    // Properly extract course IDs when editing
                                    const theoryCourseIds = extractCourseIds(student.enrolledTheoryCourses || []);
                                    const labCourseIds = extractCourseIds(student.enrolledLabCourses || []);

                                    setEditingStudent({
                                      ...student,
                                      enrolledTheoryCourses: theoryCourseIds,
                                      enrolledLabCourses: labCourseIds,
                                    });
                                    setShowEditModal(true);
                                  }
                                }}
                                disabled={!canEditStudent(student)}
                                title={
                                  canEditStudent(student)
                                    ? "Edit student"
                                    : "Only current academic year students can be edited"
                                }
                              >
                                Edit
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  )
                ) : (
                  <tr>
                    <td colSpan={7} className="text-center text-gray-500 py-4">
                      {error ? `Error: ${error}` : "-- No students found --"}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Add Student Modal */}
          {showAddModal && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
              <div className="bg-white rounded-lg w-full max-w-2xl max-h-[93vh] overflow-y-auto p-6 shadow-lg">
                <h2 className="text-xl font-bold text-red-600 mb-4">Add Student</h2>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full bg-gray-100"
                        value={selectedDept}
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Year - Sem:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full bg-gray-100"
                        value={selectedYearSem}
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Division:</label>
                      <select
                        className="border px-2 py-1.5 rounded w-full"
                        value={newStudentDiv}
                        onChange={(e) => {
                          setNewStudentDiv(e.target.value);
                          fetchAvailableBatches(e.target.value);
                        }}
                      >
                        <option value="">Select Division</option>
                        {availableDivisions.map((div) => (
                          <option key={div} value={div}>{div}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Batch:</label>
                      <select
                        className="border px-3 py-1.5 rounded w-full"
                        value={batch}
                        onChange={(e) => setBatch(e.target.value)}
                      >
                        <option value="">Select Batch</option>
                        {availableBatches.map((b) => (
                          <option key={b} value={b}>{b}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Roll No:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={rollNo}
                        onChange={(e) => setRollNo(e.target.value)}
                        placeholder="e.g., D22CO001"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Mobile Number:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={mobileNumber}
                        onChange={(e) => setMobileNumber(e.target.value)}
                        placeholder="10-digit mobile number"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Name:</label>
                    <input
                      type="text"
                      className="border px-3 py-1.5 rounded w-full"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Full name"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Email:</label>
                    <input
                      type="email"
                      className="border px-3 py-1.5 rounded w-full"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <CourseMultiSelect
                      label="Theory Courses"
                      type="theory"
                      selectedCourses={theoryCourses}
                      onChange={setTheoryCourses}
                      department={selectedDept}
                      academicYear={selectedAcademicYear}
                      currentYearSem={selectedYearSem}
                      placeholder="Select theory courses..."
                    />

                    <CourseMultiSelect
                      label="Lab Courses"
                      type="lab"
                      selectedCourses={labCourses}
                      onChange={setLabCourses}
                      department={selectedDept}
                      academicYear={selectedAcademicYear}
                      currentYearSem={selectedYearSem}
                      placeholder="Select lab courses..."
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-6 gap-3">
                  <button
                    className="px-4 py-1.5 border rounded hover:bg-gray-100"
                    onClick={() => setShowAddModal(false)}
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-[#ED1C24] text-white px-4 py-1.5 rounded hover:bg-[#c4171d]"
                    onClick={handleAddStudent}
                    disabled={loading}
                  >
                    {loading ? "Adding..." : "Add Student"}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Student Detail Modal */}
          {showDetailModal && selectedStudent && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
              <div className="bg-white rounded-lg w-full max-w-4xl max-h-[93vh] overflow-y-auto p-6 shadow-lg">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-bold text-red-600">Student Details</h2>
                  <button
                    onClick={() => setShowDetailModal(false)}
                    className="text-gray-500 hover:text-gray-700 text-2xl"
                  >
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Name:</label>
                    <p className="text-lg font-medium">{selectedStudent.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Roll Number:</label>
                    <p className="text-lg font-medium">{selectedStudent.rollNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Email:</label>
                    <p className="text-lg font-medium">{selectedStudent.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Mobile:</label>
                    <p className="text-lg font-medium">{selectedStudent.mobileNumber || "Not provided"}</p>
                  </div>
                  <div>
                    <p className="text-lg font-medium">{selectedStudent.progDept}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Academic Year:</label>
                    <p className="text-lg font-medium">{selectedStudent.academicYear}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Year - Semester:</label>
                    <p className="text-lg font-medium">{selectedStudent.currentYearSem}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Division:</label>
                    <p className="text-lg font-medium">{selectedStudent.div}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Batch:</label>
                    <p className="text-lg font-medium">{selectedStudent.batch || "Not assigned"}</p>
                  </div>
                </div>

                {/* Gradesheet Integration */}
                <div className="border-t pt-4">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">Gradesheet Integration</h3>
                  {selectedStudent._id && (
                    <StudentGradesheetIntegration student={{
                      _id: selectedStudent._id,
                      name: selectedStudent.name,
                      rollNumber: selectedStudent.rollNumber,
                      progDept: selectedStudent.progDept,
                      academicYear: selectedStudent.academicYear,
                      currentYearSem: selectedStudent.currentYearSem,
                      div: selectedStudent.div,
                      enrolledTheoryCourses: extractCourseIds(selectedStudent.enrolledTheoryCourses || []),
                      enrolledLabCourses: extractCourseIds(selectedStudent.enrolledLabCourses || []),
                    }} />
                  )}
                </div>

                <div className="flex justify-end mt-6">
                  <button
                    className="px-4 py-2 border rounded hover:bg-gray-100"
                    onClick={() => setShowDetailModal(false)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Edit Student Modal */}
          {showEditModal && editingStudent && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
              <div className="bg-white rounded-lg w-full max-w-2xl max-h-[93vh] overflow-y-auto p-6 shadow-lg">
                <h2 className="text-xl font-bold text-red-600 mb-4">Edit Student</h2>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full bg-gray-100"
                        value={editingStudent.progDept}
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Year - Sem:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full bg-gray-100"
                        value={editingStudent.currentYearSem}
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Division:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={editingStudent.div}
                        onChange={(e) => setEditingStudent({...editingStudent, div: e.target.value})}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Batch:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={editingStudent.batch || ""}
                        onChange={(e) => setEditingStudent({...editingStudent, batch: e.target.value})}
                        placeholder="e.g., A1, B2"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Roll No:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={editingStudent.rollNumber}
                        onChange={(e) => setEditingStudent({...editingStudent, rollNumber: e.target.value})}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Mobile Number:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={editingStudent.mobileNumber || ""}
                        onChange={(e) => setEditingStudent({...editingStudent, mobileNumber: e.target.value})}
                        placeholder="10-digit mobile number"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Name:</label>
                    <input
                      type="text"
                      className="border px-3 py-1.5 rounded w-full"
                      value={editingStudent.name}
                      onChange={(e) => setEditingStudent({...editingStudent, name: e.target.value})}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Email:</label>
                    <input
                      type="email"
                      className="border px-3 py-1.5 rounded w-full"
                      value={editingStudent.email}
                      onChange={(e) => setEditingStudent({...editingStudent, email: e.target.value})}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <CourseMultiSelect
                      label="Theory Courses"
                      type="theory"
                      selectedCourses={extractCourseIds(editingStudent.enrolledTheoryCourses || [])}
                      onChange={(courses) => setEditingStudent({...editingStudent, enrolledTheoryCourses: courses})}
                      department={editingStudent.progDept}
                      academicYear={editingStudent.academicYear}
                      currentYearSem={editingStudent.currentYearSem}
                      placeholder="Select theory courses..."
                    />

                    <CourseMultiSelect
                      label="Lab Courses"
                      type="lab"
                      selectedCourses={extractCourseIds(editingStudent.enrolledLabCourses || [])}
                      onChange={(courses) => setEditingStudent({...editingStudent, enrolledLabCourses: courses})}
                      department={editingStudent.progDept}
                      academicYear={editingStudent.academicYear}
                      currentYearSem={editingStudent.currentYearSem}
                      placeholder="Select lab courses..."
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-6 gap-3">
                  <button
                    className="px-4 py-1.5 border rounded hover:bg-gray-100"
                    onClick={() => setShowEditModal(false)}
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-red-600 text-white px-4 py-1.5 rounded hover:bg-red-700"
                    onClick={() => setShowDeleteConfirmModal(true)}
                    disabled={loading}
                  >
                    Delete
                  </button>
                  <button
                    className="bg-[#ED1C24] text-white px-4 py-1.5 rounded hover:bg-[#c4171d]"
                    onClick={() => setShowSaveConfirmModal(true)}
                    disabled={loading}
                  >
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Save Confirmation Modal */}
          {showSaveConfirmModal && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
              <div className="bg-white rounded-lg p-6 shadow-lg max-w-md w-full">
                <h3 className="text-lg font-semibold mb-4">Confirm Changes</h3>
                <p className="text-gray-600 mb-6">
                  Are you sure you want to save the changes to this student? This will also update their gradesheet entries.
                </p>
                <div className="flex justify-end gap-3">
                  <button
                    className="px-4 py-2 border rounded hover:bg-gray-100"
                    onClick={() => setShowSaveConfirmModal(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-[#ED1C24] text-white px-4 py-2 rounded hover:bg-[#c4171d]"
                    onClick={handleUpdateStudent}
                    disabled={loading}
                  >
                    {loading ? "Saving..." : "Save Changes"}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Delete Confirmation Modal */}
          {showDeleteConfirmModal && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
              <div className="bg-white rounded-lg p-6 shadow-lg max-w-md w-full">
                <h3 className="text-lg font-semibold mb-4">Confirm Deletion</h3>
                <p className="text-gray-600 mb-6">
                  Are you sure you want to delete this student? This action cannot be undone and will remove them from all gradesheets.
                </p>
                <div className="flex justify-end gap-3">
                  <button
                    className="px-4 py-2 border rounded hover:bg-gray-100"
                    onClick={() => setShowDeleteConfirmModal(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                    onClick={() => handleDeleteStudent()}
                    disabled={loading}
                  >
                    {loading ? "Deleting..." : "Delete Student"}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* CSV Import Modal */}
          {showCSVModal && (
            <CSVImportModal
              isOpen={showCSVModal}
              onClose={() => setShowCSVModal(false)}
              onImportSuccess={() => {
                setShowCSVModal(false);
                fetchStudents();
              }}
              defaultData={{
                progDept: selectedDept,
                academicYear: selectedAcademicYear,
                currentYearSem: selectedYearSem,
                div: activeDivision,
              }}
            />
          )}
        </main>
      </div>
    </div>
  );
};

export default AdStudents;
