import React, { useState, useRef } from "react";
import studentService from "../../services/studentService";

interface CSVImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImportSuccess: () => void;
  defaultData?: {
    progDept?: string;
    academicYear?: string;
    currentYearSem?: string;
    div?: string;
    batch?: string;
  };
}

interface ImportResult {
  totalRows: number;
  imported: number;
  errors: number;
  details: {
    success: Array<{
      row: number;
      student: any;
    }>;
    errors: Array<{
      row: number;
      data: any;
      errors: string[];
    }>;
  };
}

const CSVImportModal: React.FC<CSVImportModalProps> = ({
  isOpen,
  onClose,
  onImportSuccess,
  defaultData,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [showResults, setShowResults] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
        alert("Please select a CSV file");
        return;
      }
      setSelectedFile(file);
      setImportResult(null);
      setShowResults(false);
    }
  };

  const handleImport = async () => {
    if (!selectedFile) {
      alert("Please select a CSV file first");
      return;
    }

    try {
      setImporting(true);
      const result = await studentService.importCSV(selectedFile, defaultData);
      
      if (result.success) {
        setImportResult(result.data);
        setShowResults(true);
        
        if (result.data.imported > 0) {
          onImportSuccess();
        }
      } else {
        alert(`Import failed: ${result.message}`);
      }
    } catch (error: any) {
      console.error("Import error:", error);
      const errorMessage = error.response?.data?.message || error.message || "Import failed";
      alert(`Import failed: ${errorMessage}`);
    } finally {
      setImporting(false);
    }
  };

  const downloadTemplate = () => {
    studentService.downloadCSVTemplate();
  };

  const resetModal = () => {
    setSelectedFile(null);
    setImportResult(null);
    setShowResults(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Import Students from CSV</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
            disabled={importing}
          >
            ×
          </button>
        </div>

        {!showResults ? (
          <div>
            {/* Instructions */}
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">Instructions:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Upload a CSV file with student information</li>
                <li>• Required columns: name, email, rollNumber</li>
                <li>• Optional columns: mobileNumber, dateOfBirth, div, batch, theoryCourses, labCourses</li>
                <li>• Default values will be used for missing optional fields</li>
              </ul>
            </div>

            {/* Default Data Display */}
            {defaultData && (
              <div className="mb-4 p-3 bg-gray-50 rounded">
                <h4 className="font-medium text-gray-700 mb-2">Default Values:</h4>
                <div className="text-sm text-gray-600 grid grid-cols-2 gap-2">
                  {defaultData.progDept && <div>Department: {defaultData.progDept}</div>}
                  {defaultData.academicYear && <div>Academic Year: {defaultData.academicYear}</div>}
                  {defaultData.currentYearSem && <div>Year-Semester: {defaultData.currentYearSem}</div>}
                  {defaultData.div && <div>Division: {defaultData.div}</div>}
                  {defaultData.batch && <div>Batch: {defaultData.batch}</div>}
                </div>
              </div>
            )}

            {/* File Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select CSV File
              </label>
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={handleFileSelect}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                disabled={importing}
              />
              {selectedFile && (
                <p className="mt-2 text-sm text-green-600">
                  Selected: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                </p>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-between">
              <button
                onClick={downloadTemplate}
                className="px-4 py-2 text-blue-600 border border-blue-600 rounded hover:bg-blue-50"
                disabled={importing}
              >
                Download Template
              </button>
              <div className="space-x-2">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                  disabled={importing}
                >
                  Cancel
                </button>
                <button
                  onClick={handleImport}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                  disabled={!selectedFile || importing}
                >
                  {importing ? "Importing..." : "Import Students"}
                </button>
              </div>
            </div>
          </div>
        ) : (
          /* Results Display */
          <div>
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Import Results</h3>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="bg-blue-50 p-3 rounded text-center">
                  <div className="text-2xl font-bold text-blue-600">{importResult?.totalRows || 0}</div>
                  <div className="text-sm text-blue-700">Total Rows</div>
                </div>
                <div className="bg-green-50 p-3 rounded text-center">
                  <div className="text-2xl font-bold text-green-600">{importResult?.imported || 0}</div>
                  <div className="text-sm text-green-700">Imported</div>
                </div>
                <div className="bg-red-50 p-3 rounded text-center">
                  <div className="text-2xl font-bold text-red-600">{importResult?.errors || 0}</div>
                  <div className="text-sm text-red-700">Errors</div>
                </div>
              </div>
            </div>

            {/* Error Details */}
            {importResult && importResult.errors > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-red-700 mb-2">Errors:</h4>
                <div className="max-h-40 overflow-y-auto bg-red-50 p-3 rounded">
                  {importResult.details.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-700 mb-2">
                      <strong>Row {error.row}:</strong> {error.errors.join(", ")}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-between">
              <button
                onClick={() => {
                  setShowResults(false);
                  resetModal();
                }}
                className="px-4 py-2 text-blue-600 border border-blue-600 rounded hover:bg-blue-50"
              >
                Import Another File
              </button>
              <button
                onClick={handleClose}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CSVImportModal;
